import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/model/response/notification/notification_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../model/base/base_reponse_model.dart';
import '../../failure/failure_mapper.dart';
import '../../service/notification_service.dart';

part 'notification_repository.g.dart';

@riverpod
NotificationRepository getNotificationRepository(Ref ref) {
  final apiService = ref.read(apiNotificationServiceProvider);
  return NotificationRepositoryImpl(apiService: apiService);
}

abstract class NotificationRepository {
  Future<BaseListResponse<NotificationModel>> getNotifications({
    int? page,
    int? perPage,
  });

  Future<void> markAsRead(int notificationId);

  Future<BaseResponse<NotificationModel>> getNotificationDetail(int id);

  Future<int> getUnseenCount();
}

class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationService _apiService;

  NotificationRepositoryImpl({required NotificationService apiService})
      : _apiService = apiService;

  @override
  Future<BaseListResponse<NotificationModel>> getNotifications({
    int? page,
    int? perPage,
  }) async {
    try {
      final response =
          await _apiService.getNotifications(page: page, perPage: perPage);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseListResponse<NotificationModel>(
        data: [],
        total: 0,
        currentPage: 1,
        perPage: 10,
      );
    }
  }

  @override
  Future<void> markAsRead(int notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return;
    }
  }

  @override
  Future<BaseResponse<NotificationModel>> getNotificationDetail(int id) async {
    try {
      final response = await _apiService.getNotificationDetail(id);
      return response;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return BaseResponse<NotificationModel>(data: null);
    }
  }

  @override
  Future<int> getUnseenCount() async {
    try {
      final response = await _apiService.getUnseenCount();
      return response.data ?? 0;
    } on Exception catch (e) {
      if (FailureMapper.getFailures(e).code != 401) {
        throw FailureMapper.getFailures(e);
      }
      return 0;
    }
  }
}
