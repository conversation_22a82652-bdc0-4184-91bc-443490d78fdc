import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:dio/dio.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/flavors.dart';
import 'package:workmanager/workmanager.dart';

/// Background worker service for iOS background processing
/// Handles API calls and badge updates when app is in background
class BackgroundWorkerService {
  static const String _badgeSyncTaskName = 'badge_sync_task';
  static const String _uniqueTaskName = 'badge_sync_unique';

  /// Initialize the background worker service
  static Future<void> initialize() async {
    if (Platform.isIOS) {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to false for production
      );
    }
  }

  /// Register a one-time background task to sync badge
  static Future<void> registerBadgeSyncTask() async {
    try {
      // Get token from foreground context before passing to background
      final token = AppPreferences.getString(AppConstants.tokenKey);

      await Workmanager().registerOneOffTask(
        _uniqueTaskName,
        _badgeSyncTaskName,
        inputData: {
          'token': token ?? '',
          'baseUrl': F.baseURL,
        },
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
        initialDelay: const Duration(seconds: 1),
      );
      print('=== BACKGROUND TASK REGISTERED ===');
    } catch (e) {
      print('=== ERROR REGISTERING BACKGROUND TASK: $e ===');
    }
  }

  /// Cancel all background tasks
  static Future<void> cancelAllTasks() async {
    await Workmanager().cancelAll();
  }

  /// Sync badge count in background without Flutter context
  static Future<void> syncBadgeInBackground(
      String token, String baseUrl) async {
    try {
      if (token.isEmpty) {
        print('=== BACKGROUND SYNC SKIPPED: No auth token ===');
        return;
      }

      // Create standalone HTTP client
      final dio = Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ));

      // Make API call to get unseen count
      final response = await dio.get('/notifications/un-seen');

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final unseenCount = data['data'] as int? ?? 0;

        // Update badge using AwesomeNotifications
        if (unseenCount > 0) {
          await AwesomeNotifications().setGlobalBadgeCounter(unseenCount);
        } else {
          await AwesomeNotifications().resetGlobalBadge();
        }

        print('=== BACKGROUND BADGE SYNC SUCCESS: $unseenCount ===');
      } else {
        print('=== BACKGROUND BADGE SYNC FAILED: Invalid response ===');
      }
    } catch (e) {
      print('=== BACKGROUND BADGE SYNC ERROR: $e ===');
    }
  }
}

/// Background task callback dispatcher
/// This function runs in a separate isolate
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    print('=== BACKGROUND TASK STARTED: $task ===');
    if (Platform.isIOS) {
      return false;
    }
    try {
      switch (task) {
        case BackgroundWorkerService._badgeSyncTaskName:
          final token = inputData?['token'] as String? ?? '';
          final baseUrl = inputData?['baseUrl'] as String? ?? '';
          await BackgroundWorkerService.syncBadgeInBackground(token, baseUrl);
          break;
        default:
          print('=== UNKNOWN BACKGROUND TASK: $task ===');
          return false;
      }

      print('=== BACKGROUND TASK COMPLETED: $task ===');
      return true;
    } catch (e) {
      print('=== BACKGROUND TASK ERROR: $e ===');
      return false;
    }
  });
}
