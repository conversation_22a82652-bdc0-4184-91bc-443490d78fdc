import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/model/response/notification/notification_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../model/base/base_reponse_model.dart';
import '../networking/http_provicer.dart';

part 'notification_service.g.dart';

@riverpod
NotificationService apiNotificationService(Ref ref) {
  return NotificationService(ref.watch(dioProvider));
}

@RestApi()
abstract class NotificationService {
  factory NotificationService(Dio dio) = _NotificationService;

  @GET('/notifications/user')
  Future<BaseListResponse<NotificationModel>> getNotifications({
    @Query('page') int? page,
    @Query('per_page') int? perPage,
  });

  @PUT('/notifications/{id}')
  Future<void> markAsRead(@Path('id') int id);

  @GET('/notifications/{id}')
  Future<BaseResponse<NotificationModel>> getNotificationDetail(
      @Path('id') int id);

  @GET('/notifications/un-seen')
  Future<BaseResponse<int>> getUnseenCount();
}
