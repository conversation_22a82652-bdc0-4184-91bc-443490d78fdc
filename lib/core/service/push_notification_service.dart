import 'dart:convert';
import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/networking/http_provicer.dart';
import 'package:kitemite_app/core/service/background_worker_service.dart';
import 'package:kitemite_app/core/service/badge_service.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/features/notification/provider/notification_provider.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/features/product_management/ui/product_management.dart';
import 'package:kitemite_app/routing/router_paths.dart';

// Global navigator key to be used for navigation throughout the app
// This key is shared between regular navigation and notification navigation
final GlobalKey<NavigatorState> navigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'root');

class PushNotificationService {
  // Track app lifecycle state
  static AppLifecycleState? _currentAppState;

  // Flag to track if initial badge sync has been done
  static bool _initialBadgeSyncDone = false;

  static Future<void> initializeNotification() async {
    // Initialize Awesome Notifications
    await AwesomeNotifications().initialize(
        'resource://mipmap/launcher_icon', // Use app icon from mipmap resources
        [
          NotificationChannel(
            channelKey: 'high_importance_channel',
            channelName: 'High Importance Notifications',
            channelDescription: 'Notification channel for important alerts',
            defaultColor: Colors.blue,
            ledColor: Colors.white,
            importance: NotificationImportance.High,
            channelShowBadge: true,
          )
        ],
        debug: true);

    // Initialize notification action listeners
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: onActionReceivedMethod,
    );

    // Initialize background worker service for iOS
    await BackgroundWorkerService.initialize();

    // Initialize app lifecycle state
    _currentAppState = AppLifecycleState.resumed;

    getFirebaseToken();
  }

  static Future<void> requestNotificationPermissions() async {
    // 1) Awesome Notifications (local noti)
    final isAllowed = await AwesomeNotifications().isNotificationAllowed();
    if (!isAllowed) {
      await AwesomeNotifications().requestPermissionToSendNotifications(
        permissions: const [
          NotificationPermission.Alert,
          NotificationPermission.Sound,
          NotificationPermission.Badge,
          // (tuỳ chọn) nếu bạn dùng action button:
          // NotificationPermission.CriticalAlert,
          // NotificationPermission.Provisional, // xin quyền im lặng
        ],
      );
    }

    // 2) FCM/APNs (push noti)
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      // provisional: true, // nếu muốn xin quyền im lặng (không popup)
    );
  }

  // Hold initial notification message for cold start until Navigator is ready
  static RemoteMessage? _pendingInitialMessage;
  static int _initialMessageRetries = 0;
  static const int _maxInitialMessageRetries = 30;

  // Execute an action when a Navigator context is available
  static void _executeWhenContextReady(void Function(BuildContext) action,
      {int attempt = 0}) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      action(context);
    } else {
      if (attempt >= _maxInitialMessageRetries) {
        print('WARNING: Navigator context not available after retries');
        return;
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _executeWhenContextReady(action, attempt: attempt + 1);
      });
    }
  }

  // Try to handle the pending initial message once UI is ready
  static void _processPendingInitialMessage() {
    if (_pendingInitialMessage == null) return;
    final context = navigatorKey.currentContext;
    if (context != null) {
      print('=== PROCESSING PENDING INITIAL MESSAGE ===');
      _handleNotificationTap(_pendingInitialMessage!);
      _pendingInitialMessage = null;
      _initialMessageRetries = 0;
    } else {
      if (_initialMessageRetries >= _maxInitialMessageRetries) {
        print(
            'WARNING: Context not ready after retries; dropping pending initial message');
        return;
      }
      _initialMessageRetries++;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _processPendingInitialMessage();
      });
    }
  }

  // Method to update app lifecycle state
  static void updateAppLifecycleState(AppLifecycleState state) {
    _currentAppState = state;
    print('=== APP LIFECYCLE STATE CHANGED: $state ===');
  }

  // Check if app is in foreground
  static bool get _isAppInForeground {
    return _currentAppState == AppLifecycleState.resumed;
  }

  // Sync badge when app starts (only once per app session)
  static Future<void> syncBadgeOnAppStart() async {
    final token = AppPreferences.getString(AppConstants.tokenKey);
    if (token != null && token.isNotEmpty) {
      try {
        final context = navigatorKey.currentContext;
        if (context != null) {
          final container = ProviderScope.containerOf(context);
          final notifier =
              container.read(notificationNotifierProvider.notifier);

          // Fetch unseen count from API
          await notifier.fetchUnseenCount();

          // Get the unseen count and update badge
          final unseenCount =
              container.read(notificationNotifierProvider).unseenCount;
          await BadgeService.updateBadge(unseenCount);

          _initialBadgeSyncDone = true;
          print('=== INITIAL BADGE SYNC COMPLETED: $unseenCount ===');
        } else {
          print('=== INITIAL BADGE SYNC FAILED: Context not available ===');
        }
      } catch (e) {
        print('=== ERROR IN INITIAL BADGE SYNC: $e ===');
      }
    }
  }

  // Handle notifications from Firebase
  static Future<void> firebaseMessagingHandler() async {
    // Handle notifications when the app is open
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      // Chỉ hiển thị notification thủ công khi app đang ở foreground
      // Khi app bị terminated/background, Firebase tự động hiển thị notification

      print('=== NOTIFICATION RECEIVED ===');
      print('All data: ${message.data}');
      
      if (_isAppInForeground && Platform.isAndroid) {
        _showFirebaseNotification(message);
      } else {
        print('=== SKIPPING MANUAL NOTIFICATION (App not in foreground) ===');
      }

      // Update unseen notification count when notification is received
      await _updateUnseenCount();
    });

    // Handle notification tap when the app is opened
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _executeWhenContextReady((_) {
        _handleNotificationTap(message);
      });
    });

    // Check if app was opened from a notification (cold start)
    // gọi lần đầu tiên khi mở app
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _pendingInitialMessage = initialMessage;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _processPendingInitialMessage();
      });
    }
  }

  // Function to update the unseen notification count and badge
  static Future<void> _updateUnseenCount() async {
    try {
      // Get the NavigatorState context
      final context = navigatorKey.currentContext;
      if (context != null) {
        // Get container and notifier before async operations
        final container = ProviderScope.containerOf(context);
        final notifier = container.read(notificationNotifierProvider.notifier);

        // Call fetchUnseenCount
        await notifier.fetchUnseenCount();

        // Get the updated unseen count after fetch
        final unseenCount =
            container.read(notificationNotifierProvider).unseenCount;

        // Update badge only on iOS and only when app is not in foreground

        await BadgeService.updateBadge(unseenCount);
      }
    } catch (e) {}
  }

  static void _showFirebaseNotification(RemoteMessage message) {
    print('=== SHOWING NOTIFICATION ===');
    print('All data: ${message.data}');
    talkerApp.debug('All data: ${message.data}');

    AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
        channelKey: 'high_importance_channel',
        title: message.notification?.title,
        body: message.notification?.body,
        notificationLayout: NotificationLayout.Default,
        payload:
            message.data.map((key, value) => MapEntry(key, value.toString())),
        icon: 'resource://mipmap/launcher_icon',
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'VIEW',
          label: 'View',
        )
      ],
    );
  }

  static void _handleNotificationTap(RemoteMessage message) {
    print('=== HANDLING NOTIFICATION TAP ===');
    print('All data: ${message.data}');
    talkerApp.debug('All data: ${message.data}');
    // Default behavior for other notifications
    final notificationId = message.data['id'] != null
        ? int.tryParse(message.data['id'].toString())
        : null;
    // Check if this is a sold-histories notification
    if (message.data['screen'] == 'sold-histories') {
      print('=== HANDLING SOLD-HISTORIES NOTIFICATION ===');

      // Extract product_id from config string
      final configString = message.data['config'] as String;
      final configJson = json.decode(configString) as Map<String, dynamic>;
      final productId = configJson['product_id'].toString();
      final orderId = configJson['order_id'].toString();

      navigateToProductManagementHistory(
        productId.toString(),
        orderId.toString(),
        notificationId ?? 0,
      );
      return;
    }

    print('ID extracted: $notificationId');

    if (notificationId != null) {
      navigateToNotificationDetail(notificationId);
    } else {
      print('WARNING: Could not extract notification ID from data');
    }
  }

  // Navigate to notification detail screen
  static void navigateToNotificationDetail(int notificationId) {
    // Use the navigator key to navigate
    final context = navigatorKey.currentContext;
    if (context != null && context.mounted) {
      // Add additional delay to ensure all widgets are fully rendered
      Future.delayed(const Duration(milliseconds: 100), () {
        final newContext = navigatorKey.currentContext;
        if (newContext != null && newContext.mounted) {
          GoRouter.of(newContext)
              .push(RouterPaths.notificationDetail, extra: notificationId);
        }
      });
    } else {
      print(
          'WARNING: Navigator context not available for notification navigation');
    }
  }

  // Navigate to Product Management History tab with product highlighting
  static void navigateToProductManagementHistory(
      String productId, String orderId, int idNoti) {
    print('=== NAVIGATING TO PRODUCT MANAGEMENT HISTORY ===');
    print('Product ID: $productId');

    final context = navigatorKey.currentContext;
    if (context != null && context.mounted) {
      // Add additional delay to ensure all widgets are fully rendered
      Future.delayed(const Duration(milliseconds: 100), () {
        final newContext = navigatorKey.currentContext;
        if (newContext != null && newContext.mounted) {
          ProviderScope.containerOf(newContext)
              .read(productManagementNotifierProvider.notifier)
              .refresh();

          ProviderScope.containerOf(newContext)
              .read(notificationNotifierProvider.notifier)
              .markAsRead(idNoti);
          // Navigate to Product Management with History tab and product highlighting
          newContext.go(RouterPaths.business,
              extra: ProductManagementArg(
                initialTabIndex: 2,
                highlightProductId: productId,
                highlightOrderId: orderId,
              ));
        }
      });
    } else {
      print('WARNING: Navigator context not available');
    }
  }

  static Future<String?> getFirebaseToken() async {
    return await FirebaseMessaging.instance.getToken().then((token) {
      print('-----------------Firebase Token: $token');
      AppPreferences.saveString(AppConstants.firebaseToken, token ?? "");
      return token;
    }).catchError((error) {
      print('----------------Error getting Firebase token: $error');
      return error;
    });
  }
}

// Static method to be used as entry point for notification actions
@pragma('vm:entry-point')
Future<void> onActionReceivedMethod(ReceivedAction receivedAction) async {
  // Default behavior for other notification actions
  final notificationId = receivedAction.payload?['id'] != null
      ? int.tryParse(receivedAction.payload!['id']!)
      : null;
  // Check if this is a sold-histories notification action
  if (receivedAction.payload?['screen'] == 'sold-histories') {
    print('=== HANDLING SOLD-HISTORIES ACTION ===');

    try {
      // Extract product_id from config string
      final configString = receivedAction.payload!['config'] as String;
      final configJson = json.decode(configString) as Map<String, dynamic>;
      final orderId = configJson['order_id'].toString();
      final productId = configJson['product_id'].toString();

      PushNotificationService.navigateToProductManagementHistory(
          productId, orderId, notificationId ?? 0);
      await PushNotificationService._updateUnseenCount();
      return;
    } catch (e) {
      print('Error parsing config: $e');
      return;
    }
  }

  print('ID extracted from action: $notificationId');

  if (notificationId != null) {
    PushNotificationService.navigateToNotificationDetail(notificationId);
    await PushNotificationService._updateUnseenCount();
  } else {
    print('WARNING: Could not extract notification ID from action payload');
  }
}

// Background handler
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('=== FIREBASE BACKGROUND MESSAGE RECEIVED ===');

  // Use background worker service for iOS badge sync
  // Don't use AppPreferences.init() here as it causes platform channel errors

  await BackgroundWorkerService.registerBadgeSyncTask();

  // Note: For Android, badge sync will be handled when app returns to foreground
}
