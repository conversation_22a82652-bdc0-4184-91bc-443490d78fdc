import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/ui/base_search_widget.dart';
import 'package:kitemite_app/core/common/widgets/error_widget/error_common_widget.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/core/utils/string_extension.dart';
import 'package:kitemite_app/features/home/<USER>/detail_store_seller_provider.dart';
import 'package:kitemite_app/features/map_store/provider/handle_navigator_provider/handle_navigator_provider.dart';
import 'package:kitemite_app/features/map_store/ui/register_product/select_cabinet.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/shopping_cart/ui/shopping_cart_screen.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/dialog_help_open_lock.dart';
import 'package:kitemite_app/features/shopping_cart/widget/dialog_open_lock_widget.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';
import 'package:permission_handler/permission_handler.dart';

final _statusLockProvider = StateProvider<Map<int, bool>>((_) => {});

class DetailStoreSeller extends ConsumerStatefulWidget {
  const DetailStoreSeller({super.key, required this.storeId});
  final int storeId;

  @override
  ConsumerState<DetailStoreSeller> createState() => _DetailStoreSellerState();
}

class _DetailStoreSellerState extends ConsumerState<DetailStoreSeller> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(detailStoreSellerNotifierProvider.notifier)
          .loadWarehouseDetail(widget.storeId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(detailStoreSellerNotifierProvider);
    final warehouse = state.warehouse;

    return BaseScaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppbar.basic(
        title: warehouse?.name ?? "",
        onTap: () {
          context.pop();
        },
        actions: [
          PopupMenuButton<String>(
            color: Colors.white,
            surfaceTintColor: Colors.white,
            padding: const EdgeInsets.only(top: 8),
            offset: const Offset(0, 40),
            elevation: 2,
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              PopupMenuItem<String>(
                value: '1',
                height: 12,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                onTap: () {
                  context.pop();
                },
                child: SizedBox(
                  height: 30,
                  child: Center(
                    child: Text(
                      '閉じる',
                      style: AppTextStyles.regular(14.sp,
                          color: AppColors.textPrimary),
                    ),
                  ),
                ),
              ),
            ],
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: state.isLoading
          ? const Center(child: CircularProgressIndicator())
          : state.error != null
              ? Center(
                  child: ErrorCommonWidget(
                  error: state.error!,
                  onPressed: () {
                    ref
                        .read(detailStoreSellerNotifierProvider.notifier)
                        .loadWarehouseDetail(widget.storeId);
                  },
                ))
              : warehouse == null
                  ? const Center(child: Text('No store data'))
                  : SafeArea(
                      top: false,
                      maintainBottomViewPadding: true,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${warehouse.province ?? ""}, ${warehouse.city ?? ""}, ${warehouse.street ?? ""}",
                                      style: AppTextStyles.regular(14.sp,
                                          color: AppColors.textLightSecondary),
                                    ),
                                    SizedBox(height: 8.h),
                                    BaseSearchWidget(
                                      onSearch: (keyword) {
                                        ref
                                            .read(
                                                detailStoreSellerNotifierProvider
                                                    .notifier)
                                            .searchProducts(keyword);
                                      },
                                    ),
                                  ])),
                          SizedBox(height: 8.h),
                          if (state.searchKeyword.isNotEmpty &&
                              warehouse.cabinets != null &&
                              warehouse.cabinets!.every((cabinet) {
                                final hasMatchingProducts = cabinet.products
                                        ?.any((product) =>
                                            product.name
                                                ?.toLowerCase()
                                                .contains(state.searchKeyword
                                                    .toLowerCase()) ??
                                            false) ??
                                    false;
                                return !hasMatchingProducts;
                              }))
                            Expanded(
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Assets.iconEmptyProduct.image(),
                                    SizedBox(height: 16.h),
                                    Text(
                                      '商品を見つかりません。 "${state.searchKeyword}"',
                                      style: AppTextStyles.regular(14.sp,
                                          color: AppColors.textLightSecondary),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          SizedBox(height: 8.h),
                          Expanded(
                            child: _buildFreezerList(warehouse),
                          ),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildFreezerList(WarehouseModel warehouse) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: warehouse.cabinets?.length ?? 0,
      itemBuilder: (context, index) {
        final cabinet = warehouse.cabinets![index];
        // Filter products based on search keyword
        final filteredProducts = cabinet.products?.where((product) {
              final searchKeyword = ref
                  .watch(detailStoreSellerNotifierProvider)
                  .searchKeyword
                  .toLowerCase();
              return product.name?.toLowerCase().contains(searchKeyword) ??
                  false;
            }).toList() ??
            [];

        // Skip cabinet if no products match search and there is a search keyword
        if (filteredProducts.isEmpty &&
            ref
                .watch(detailStoreSellerNotifierProvider)
                .searchKeyword
                .isNotEmpty) {
          return const SizedBox.shrink();
        }

        return _buildFreezerSection(cabinet);
      },
    );
  }

  Widget _buildFreezerSection(CabinetModel cabinet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFreezerHeader(cabinet),
        SizedBox(height: 8.h),
        _buildFreezerItems(cabinet),
        SizedBox(height: 24.h),
      ],
    );
  }

  /// Kiểm tra quyền Bluetooth và Location
  Future<bool> _checkPermissions() async {
    var isDenied = false;
    Map<Permission, PermissionStatus> statuses = await [
      Permission.location,
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
    ].request();

    for (var status in statuses.entries) {
      if (status.key == Permission.location) {
        if (status.value.isGranted) {
          //debugPrint('Location permission granted');
        } else {
          isDenied = true;
          // debugPrint("Location permission not granted");
        }
      } else if (status.key == Permission.bluetooth) {
        if (status.value.isGranted) {
        } else {
          isDenied = true;
          // debugPrint('Bluetooth scan permission not granted');
        }
      }
    }

    if (isDenied) {
      if (mounted) {
        showAlertDialog(context);
      }
      return false;
    }

    return true;
  }

  /// Cập nhật trạng thái khóa và navigate
  void _updateLockStatusAndNavigate(CabinetModel cabinet) {
    ref.read(_statusLockProvider.notifier).state = {
      ...ref.read(_statusLockProvider),
      cabinet.id ?? 0: true
    };
    context.push(RouterPaths.chooseProductSeller, extra: cabinet);
  }

  /// Hiển thị dialog mở khóa
  Future<bool?> _showUnlockDialog(CabinetModel cabinet) async {
    bool ispush = false;
    final parentContext = context; // Store the parent context
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: DialogOpenLockWidget(
          lockId: cabinet.lock?.name ?? "",
          openSuccess: () {
            if (ispush) return;
            ispush = true;
            _updateLockStatusAndNavigate(cabinet);
          },
          onTapByPass: () async {
            if (ispush) return;

            try {
              // Use the parent context instead of the dialog context
              final confirmed =
                  await showBypassConfirmDialogNew(parentContext, true);

              // Check if widget is still mounted after first dialog
              if (!mounted) return;

              if (confirmed == true) {
                // Use parent context for second dialog as well
                final confirmed1 =
                    await showBypassConfirmDialog(parentContext, true);

                // Check if widget is still mounted after second dialog
                if (!mounted) return;

                if (confirmed1 == true) {
                  ispush = true;
                  _updateLockStatusAndNavigate(cabinet);
                }
              }
              if (confirmed == false) {
                if (!mounted) return;
                _showUnlockDialog(cabinet);
              }
            } catch (e) {
              // Handle any errors that might occur during dialog operations
              debugPrint('Error in onTapByPass: $e');
            }
          },
        ),
      ),
    );

    if (result == true) {
      if (!ispush) {
        _updateLockStatusAndNavigate(cabinet);
      }
    }

    return result;
  }

  /// Xử lý logic kết nối khóa chính
  Future<void> _handleConnectLock(CabinetModel cabinet) async {
    final hasPermission = await _checkPermissions();
    if (!hasPermission) return;

    await _showUnlockDialog(cabinet);
  }

  showAlertDialog(BuildContext context) {
    // set up the button
    Widget okButton = TextButton(
      child: const Text("設定へ"),
      onPressed: () {
        Navigator.of(context).pop();
        if (Platform.isIOS) {
          const OpenSettingsPlusIOS().bluetooth();
        } else {
          const OpenSettingsPlusAndroid().bluetooth();
        }
      },
    );

    Widget cancelButton = TextButton(
      child: const Text("閉じる"),
      onPressed: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      content: const Text("Bluetoothの使用権限がありません。権限を許可してください。"),
      actions: [
        cancelButton,
        okButton,
      ],
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  Widget _buildFreezerHeader(CabinetModel cabinet) {
    final state = ref.watch(detailStoreSellerNotifierProvider);
    final warehouse = state.warehouse;
    return Builder(
      builder: (context) => Row(
        children: [
          Text(
            "冷凍庫-${cabinet.cabinetCode ?? ""}",
            style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
          ),
          const Spacer(),
          if ((warehouse?.isActive == false) &&
              cabinet.isCabinetActive == false) ...[
            InkWell(
              onTap: () {
                context.push(RouterPaths.selectCabinet,
                    extra: StorageSelectionScreenArg(
                        storeId: widget.storeId, cabinetId: cabinet.id ?? 0));
              },
              child: SvgPicture.asset(Assets.iconAdd1svg),
            ),
            SizedBox(width: 10.w),
            ...[
              if (ref.watch(_statusLockProvider)[cabinet.id] == true) ...[
                InkWell(
                  onTap: () {
                    context.push(RouterPaths.chooseProductSeller,
                        extra: cabinet);
                  },
                  child: SvgPicture.asset(Assets.iconEditSvg),
                ),
                SizedBox(width: 10.w),
              ],
            ],
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                minimumSize: const Size(0, 28),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              onPressed: () => _handleConnectLock(cabinet),
              child: Row(
                children: [
                  Text(
                    "解除",
                    style: AppTextStyles.regular(12.sp,
                        color: AppColors.textPrimary),
                  ),
                  SizedBox(width: 8.w),
                  const Icon(
                    Icons.lock,
                    color: AppColors.textPrimary,
                  )
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFreezerItems(CabinetModel cabinet) {
    final state = ref.watch(detailStoreSellerNotifierProvider);
    final warehouse = state.warehouse;
    final searchKeyword = ref
        .watch(detailStoreSellerNotifierProvider)
        .searchKeyword
        .toLowerCase();
    final filteredProducts = cabinet.products?.where((product) {
          return product.name?.toLowerCase().contains(searchKeyword) ?? false;
        }).toList() ??
        [];

    return Column(
      children: filteredProducts
          .map((product) => GestureDetector(
                onTap: () {
                  ref
                      .read(handleNavigatorNotifierProvider.notifier)
                      .setPopToRouteUpdate(RouterPaths.detailStoreSeller);
                  context
                      .push(RouterPaths.productDetail,
                          extra: ProductDetailScreenArg(
                              productId: product.id ?? 0,
                              isUpdateProduct: true,
                              isLoginWithAccount: true,
                              isProductActive:
                                  !((warehouse?.isActive == false) &&
                                      product.shelf?.isShelfActive == false)))
                      .then((value) {
                    ref
                        .read(detailStoreSellerNotifierProvider.notifier)
                        .loadWarehouseDetail(widget.storeId);
                  });
                },
                child: _buildSlidableItem(
                    key: ValueKey(product.id),
                    image: product.img ?? "",
                    title: product.name ?? "",
                    price: "${product.price?.priceString()}円",
                    quantity: product.quantity?.toString() ?? "0",
                    expiryDate: product.expirationDate ?? "",
                    sheftCode: product.shelf?.shelfCode ?? "",
                    productId: product.id ?? 0,
                    enabled: ((warehouse?.isActive == false) &&
                        product.shelf?.isShelfActive == false),
                    isActive: product.shelf?.isShelfActive ?? false,
                    isShowStatus: ((warehouse?.isActive == false) &&
                        cabinet.isCabinetActive == false)),
              ))
          .toList(),
    );
  }

  Widget _buildSlidableItem(
      {Key? key,
      required String image,
      required String title,
      required String price,
      required String quantity,
      required String expiryDate,
      required String sheftCode,
      required int productId,
      required bool enabled,
      required bool isActive,
      required bool isShowStatus}) {
    return Slidable(
      key: key,
      enabled: enabled,
      endActionPane: ActionPane(
        extentRatio: 0.15,
        motion: const ScrollMotion(),
        children: [
          CustomSlidableAction(
            onPressed: (context) {
              ref
                  .read(detailStoreSellerNotifierProvider.notifier)
                  .deleteProduct(id: productId, warehouseId: widget.storeId);
            },
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.delete,
                  color: Colors.white,
                ),
                SizedBox(height: 2.h),
                Text(
                  "編集",
                  style: AppTextStyles.regular(12.sp, color: Colors.white),
                ),
              ],
            ),
          )
        ],
      ),
      child: ShoppingCartItem(
        image: image,
        title: title,
        price: price,
        quantity: quantity,
        expiryDate: expiryDate,
        sheftCode: sheftCode,
        isActive: isActive,
        isShowStatus: isShowStatus,
      ),
    );
  }
}

class ShoppingCartItem extends StatelessWidget {
  final String image;
  final String title;
  final String price;
  final String quantity;
  final String expiryDate;
  final String sheftCode;
  final bool isActive;
  final bool isShowStatus;

  const ShoppingCartItem({
    super.key,
    required this.image,
    required this.title,
    required this.price,
    required this.quantity,
    required this.expiryDate,
    required this.sheftCode,
    required this.isActive,
    required this.isShowStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.network(image,
                  width: 80.w, height: 80.h, fit: BoxFit.cover)),
          const SizedBox(width: 8),
          Expanded(
            child: SizedBox(
              height: 80.h,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(title,
                          style: AppTextStyles.bold(14.sp,
                              color: AppColors.textPrimary)),
                      const Spacer(),
                      Text(
                        "有効期限: $expiryDate",
                        style: AppTextStyles.regular(12.sp,
                            color: AppColors.textLightSecondary),
                      ),
                      const Spacer(),
                      Center(
                        child: Row(
                          children: [
                            Expanded(
                                child: Text(
                              "No. $sheftCode",
                              style: AppTextStyles.regular(12.sp,
                                  color: AppColors.textLightSecondary),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            )),
                            SizedBox(width: 16.w),
                            Text(
                              "QTY: $quantity",
                              style: AppTextStyles.regular(12.sp,
                                  color: AppColors.textLightSecondary),
                            ),
                            SizedBox(width: 16.w),
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                      text: "合計: ",
                                      style: AppTextStyles.regular(12.sp,
                                          color: AppColors.textLightSecondary)),
                                  TextSpan(
                                    text: price,
                                    style: AppTextStyles.regular(12.sp,
                                        color: AppColors.primary),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  )),
                  if (isShowStatus) ...[
                    SizedBox(width: 8.w),
                    SvgPicture.asset(
                      isActive ? Assets.checked : Assets.uncheck,
                    ),
                  ]
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
