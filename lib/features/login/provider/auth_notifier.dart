import 'dart:io';

import 'package:kitemite_app/core/common/constans/app_contans.dart';
import 'package:kitemite_app/core/failure/app_failure.dart';
import 'package:kitemite_app/core/repository/auth/auth_repository.dart';
import 'package:kitemite_app/core/service/push_notification_service.dart';
import 'package:kitemite_app/core/utils/app_preferences.dart';
import 'package:kitemite_app/core/utils/app_version_service.dart';
import 'package:kitemite_app/model/request/auth/user_request.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'auth_state.dart';

part 'auth_notifier.g.dart';

@Riverpod(keepAlive: true)
class AuthNotifier extends _$AuthNotifier {
  late final AuthRepository repo;

  @override
  AsyncValue<AuthState> build() {
    repo = ref.read(getAuthRepositoryProvider);
    return const AsyncValue.data(AuthState());
  }

  void toggleAgree1() {
    state = state.whenData(
        (value) => value.copyWith(agree1: !value.agree1, loginFailure: null));
  }

  void toggleAgree2() {
    state = state.whenData(
        (value) => value.copyWith(agree2: !value.agree2, loginFailure: null));
  }

  void toggleAgree3() {
    state = state.whenData(
        (value) => value.copyWith(agree3: !value.agree3, loginFailure: null));
  }

  Future<void> login(String email) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isLoading: true,
          loginFailure: null,
        ));
    try {
      await repo.login(email: email);
      state = const AsyncValue.data(
          AuthState(isLoading: false, loginSuccess: true));
    } on AppFailure catch (e) {
      state =
          AsyncValue.data(AuthState(isLoading: false, loginFailure: e.message));
    }
  }

  Future<void> verifyOtp(String email, String otp) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isLoading: true,
          verifyOtpFailure: null,
        ));
    try {
      final response = await repo.verifyOTP(email: email, otp: otp);
      await AppPreferences.saveString(
          AppConstants.tokenKey, response.data?.token ?? "");

      // Store device info after successful verification
      if (response.data?.user?.id != null) {
        await _registerDevice(response.data!.user!.id);
      }

      state = AsyncValue.data(AuthState(
          isFirstLogin: response.data?.user?.username == null,
          userInfoModel: response.data?.user,
          isLoading: false));
    } on AppFailure catch (e) {
      state = AsyncValue.data(
          AuthState(isLoading: false, verifyOtpFailure: e.message));
    }
  }

  Future<void> _registerDevice(int userId) async {
    try {
      var firebaseToken = AppPreferences.getString(AppConstants.firebaseToken);
      if (firebaseToken == null || firebaseToken.isEmpty) {
        await PushNotificationService.getFirebaseToken();
        firebaseToken = AppPreferences.getString(AppConstants.firebaseToken);
      }
      String? deviceId = await getDeviceId();
      await repo.storeDevice(
        userID: userId,
        platform: Platform.isIOS ? "ios" : "android",
        deviceToken: firebaseToken ?? "",
        deviceId: deviceId ?? "",
      );
      state = state.whenData(
          (prevState) => prevState.copyWith(isDeviceRegistered: true));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) =>
          prevState.copyWith(deviceRegistrationFailure: e.message));
    }
  }

  Future<void> reSentOtp(String email) async {
    try {
      state = state.whenData((prevState) => prevState.copyWith(
            verifyOtpFailure: null,
          ));
      final _ = await repo.resentOTP(email: email);
    } on AppFailure catch (e) {
      state = AsyncValue.data(
          AuthState(isLoading: false, verifyOtpFailure: e.message));
    }
  }

  Future<void> updateProfile(UserRequest request, int userID) async {
    state = state.whenData((prevState) => prevState.copyWith(
          isLoading: true,
          upDateProfileFailure: null,
          isUpdateprofileSuccess: false,
        ));
    try {
      final urlImage = state.value?.urlImage;
      request = request.copyWith(img: urlImage);
      await repo.updateUser(userID: userID, request: request);
      state = state.whenData((prevState) =>
          prevState.copyWith(isLoading: false, isUpdateprofileSuccess: true));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) => prevState.copyWith(
            isLoading: false,
            upDateProfileFailure: e.message,
          ));
    }
  }

  Future<void> upFileImage(String imageBase64) async {
    try {
      state = state.whenData((prevState) => prevState.copyWith(
            upFileImageFailure: null,
            isUploadingImage: true,
          ));
      final result = await repo.upLoadFile(base64: imageBase64);
      state = state.whenData((prevState) => prevState.copyWith(
            urlImage: result.links?.first ?? "",
            isLoading: false,
            isUploadingImage: false,
          ));
    } on AppFailure catch (e) {
      state = state.whenData((prevState) => prevState.copyWith(
            isLoading: false,
            isUploadingImage: false,
            upFileImageFailure: e.message,
          ));
    }
  }

  Future<void> upDateImageAvatar(String imageURL) async {
    state = state.whenData((prevState) => prevState.copyWith(
          urlImage: imageURL,
        ));
  }

  void reset() {
    state = const AsyncValue.data(AuthState());
  }
}
