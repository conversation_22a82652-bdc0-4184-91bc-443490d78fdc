// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileProviderHash() => r'530256081350c4b75f751216498359f0d49838fc';

/// See also [ProfileProvider].
@ProviderFor(ProfileProvider)
final profileProviderProvider =
    AsyncNotifierProvider<ProfileProvider, ProfileState>.internal(
  ProfileProvider.new,
  name: r'profileProviderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileProviderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ProfileProvider = AsyncNotifier<ProfileState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
