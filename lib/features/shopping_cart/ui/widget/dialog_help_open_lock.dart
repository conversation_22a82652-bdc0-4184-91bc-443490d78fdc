import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';

Future<bool?> showBypassConfirmDialogNew(
    BuildContext context, bool isSeller) async {
  return await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => _BypassConfirmDialog(isSeller: isSeller),
  );
}

class _BypassConfirmDialog extends StatefulWidget {
  const _BypassConfirmDialog({required this.isSeller});
  final bool isSeller;
  @override
  _BypassConfirmDialogState createState() => _BypassConfirmDialogState();
}

class _BypassConfirmDialogState extends State<_BypassConfirmDialog> {
  Timer? _timer;
  int _countdown = 59;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 1) {
        setState(() {
          _countdown--;
        });
      } else {
        _timer?.cancel();
        if (mounted) {
          Navigator.of(context).pop(false);
        }
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 327.w,
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Title
            SizedBox(
              width: double.infinity,
              child: Text(
                widget.isSeller ? "鍵に接続できない？" : '鍵に接続できない？',
                style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
                textAlign: TextAlign.left,
              ),
            ),
            SizedBox(height: 16.h),

            // Instructions
            SizedBox(
              width: double.infinity,
              child: Text(
                widget.isSeller
                    ? "1. アプリの操作をせず、鍵の操作から1分以上放置する \n2. 再度、鍵を指で触る（鍵が開く動作） \n3. アプリで接続ボタンを押す"
                    : '1.アプリの操作をせず、鍵の操作から1分以上放置する\n2.再度、鍵を指で触る（鍵が開く動作）\n3.アプリで接続ボタンを押す',
                style: AppTextStyles.regular(
                  14.sp,
                  color: AppColors.textPrimary,
                  height: 1.57,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            SizedBox(height: 16.h),

            // Buttons
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFB92A),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () => Navigator.of(context).pop(false),
                        child: Center(
                          child: Text(
                            '戻る（${_countdown}s）',
                            style: AppTextStyles.bold(15.sp,
                                color: const Color(0xFF161C24)),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Container(
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      border: Border.all(
                        color: const Color(0xFFFFB92A),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () => Navigator.of(context).pop(true),
                        child: Center(
                          child: Text(
                            widget.isSeller ? "接続せずに進む" : 'このまま決済へ',
                            style: AppTextStyles.bold(15.sp,
                                color: const Color(0xFF161C24)),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
