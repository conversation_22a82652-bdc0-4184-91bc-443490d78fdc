import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/features/shopping_cart/widget/open_lock_provider.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';

class DialogOpenLockWidget extends ConsumerStatefulWidget {
  final String lockId;
  const DialogOpenLockWidget(
      {super.key,
      required this.openSuccess,
      required this.lockId,
      this.onTapByPass});
  final VoidCallback openSuccess;
  final VoidCallback? onTapByPass;

  @override
  ConsumerState<DialogOpenLockWidget> createState() =>
      _DialogOpenLockWidgetState();
}

class _DialogOpenLockWidgetState extends ConsumerState<DialogOpenLockWidget>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(openLockNotifierProvider.notifier).initialize(
            widget.lockId,
          );
      //get location
      ref.read(openLockNotifierProvider.notifier).scanBle();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      Future.delayed(const Duration(milliseconds: 500), () {
        final status = ref.read(openLockNotifierProvider).status;
        if (status != BleStatus.scanning) {
          ref.read(openLockNotifierProvider.notifier).scanBle();
        }
      });
    }
  }

  showAlertDialog(BuildContext context) {
    // set up the button
    Widget okButton = TextButton(
      child: const Text("設定へ"),
      onPressed: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
        if (Platform.isIOS) {
          const OpenSettingsPlusIOS().bluetooth();
        } else {
          const OpenSettingsPlusAndroid().bluetooth();
        }
      },
    );

    Widget cancelButton = TextButton(
      child: const Text("閉じる"),
      onPressed: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      content: const Text("Bluetoothの使用権限がありません。権限を許可してください。"),
      actions: [
        cancelButton,
        okButton,
      ],
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(openLockNotifierProvider, (previous, current) {
      if (current.status == BleStatus.openSuccess) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pop();
            widget.openSuccess.call();
          }
        });
      }
      if (current.status == BleStatus.notHavePermission) {
        showAlertDialog(context);
      }
    });
    final status = ref.watch(openLockNotifierProvider).status;
    final isConnected = status == BleStatus.connected;
    final isOpenLock = status == BleStatus.openSuccess;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Align(
            alignment: Alignment.topRight,
            child: GestureDetector(
              onTap: () {
                final status = ref.watch(openLockNotifierProvider).status;
                final isOpenLock = status == BleStatus.openSuccess;
                if (isOpenLock) {
                  widget.openSuccess.call();
                }
                Navigator.of(context).pop();
              },
              child: const Icon(Icons.close),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 24, left: 24, right: 24),
          child: Column(children: [
            Text(
              "鍵の〇に指を触れてください",
              style: AppTextStyles.bold(16.sp),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              "(1分間赤ランプ点灯)",
              style: AppTextStyles.regular(14.sp),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            GestureDetector(
                onTap: () {
                  if (status == BleStatus.connected) {
                    ref.read(openLockNotifierProvider.notifier).openLock();
                  }
                },
                child: asset(status).image()),
            if (status != BleStatus.init)
              const SizedBox(
                height: 14,
              ),
            if (status != BleStatus.init)
              Text(
                isConnected
                    ? "Bluetooth接続済"
                    : isOpenLock
                        ? "鍵を解除しました。"
                        : "Bluetooth未接続",
                style: AppTextStyles.regular(12.sp,
                    color: isConnected
                        ? const Color(0xFFFAA97F)
                        : isOpenLock
                            ? const Color(0xFF0A3D91)
                            : const Color(0xFFFAA97F)),
              ),
            if (ref.read(openLockNotifierProvider.notifier).bluetoothDevice !=
                null)
              const SizedBox(
                height: 8,
              ),
            if (ref.read(openLockNotifierProvider.notifier).bluetoothDevice !=
                    null &&
                !isOpenLock)
              Text(
                "ID: ${ref.read(openLockNotifierProvider.notifier).bluetoothDevice?.platformName ?? ""}",
                style: AppTextStyles.regular(12.sp,
                    color: AppColors.textLightSecondary),
              ),
          ]),
        ),
        if (!isOpenLock) ...[
          const Divider(color: AppColors.mono40, thickness: 1, height: 1),
          TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onTapByPass?.call();
              },
              child: Row(
                children: [
                  const Divider(color: AppColors.textLightSecondary),
                  SvgPicture.asset(Assets.icHelp),
                  SizedBox(width: 8.w),
                  Text(
                    "ヘルプ",
                    style: AppTextStyles.regular(12.sp,
                        color: AppColors.textLightSecondary),
                  ),
                ],
              ))
        ]
      ],
    );
  }

  AssetGenImage asset(BleStatus status) {
    if (status == BleStatus.init) {
      return Assets.iconBleConnect;
    } else if (status == BleStatus.connected) {
      return Assets.iconBleConnecting;
    } else if (status == BleStatus.openSuccess) {
      return Assets.unLock;
    } else {
      return Assets.iconBleConnect;
    }
  }
}
