// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/widgets.dart';

class $AssetsAnimationsGen {
  const $AssetsAnimationsGen();

  /// File path: assets/animations/loading.json
  String get loading => 'assets/animations/loading.json';

  /// List of all assets
  List<String> get values => [loading];
}

class Assets {
  const Assets._();

  static const AssetGenImage a3159511881580040079 =
      AssetGenImage('assets/315951188_1580040079.jpg');
  static const AssetGenImage aboutOurTeam =
      AssetGenImage('assets/about-our-team.png');
  static const $AssetsAnimationsGen animations = $AssetsAnimationsGen();
  static const AssetGenImage appIcon = AssetGenImage('assets/app icon.png');
  static const AssetGenImage appIcon2 = AssetGenImage('assets/app_icon_2.png');
  static const AssetGenImage avatarDefault =
      AssetGenImage('assets/avatar_default.png');
  static const String buttonSearch = 'assets/button_search.svg';
  static const String checked = 'assets/checked.svg';
  static const String floatingAdd = 'assets/floating_add.svg';
  static const String icHelp = 'assets/ic_help.svg';
  static const String iconAdd = 'assets/icon_add.svg';
  static const String iconAdd1svg = 'assets/icon_add_1svg.svg';
  static const AssetGenImage iconBleConnect =
      AssetGenImage('assets/icon_ble_connect.png');
  static const AssetGenImage iconBleConnectedPng =
      AssetGenImage('assets/icon_ble_connected.png');
  static const String iconBleConnectedSvg = 'assets/icon_ble_connected.svg';
  static const AssetGenImage iconBleConnecting =
      AssetGenImage('assets/icon_ble_connecting.png');
  static const String iconBleNotConnect = 'assets/icon_ble_not_connect.svg';
  static const AssetGenImage iconDirection =
      AssetGenImage('assets/icon_direction.png');
  static const AssetGenImage iconEditPng =
      AssetGenImage('assets/icon_edit.png');
  static const String iconEditSvg = 'assets/icon_edit.svg';
  static const AssetGenImage iconEmptyProduct =
      AssetGenImage('assets/icon_empty_product.png');
  static const AssetGenImage iconNotiPng =
      AssetGenImage('assets/icon_noti.png');
  static const String iconNotiSvg = 'assets/icon_noti.svg';
  static const String iconRemove = 'assets/icon_remove.svg';
  static const AssetGenImage image158 = AssetGenImage('assets/image 158.png');
  static const AssetGenImage kitemite1920x10802Png =
      AssetGenImage('assets/kitemite_1920x1080 - コピー (2).png');
  static const String kitemite1920x10802Svg =
      'assets/kitemite_1920x1080 - コピー (2).svg';
  static const String lock24h = 'assets/lock24h.svg';
  static const AssetGenImage markerIos = AssetGenImage('assets/marker_ios.png');
  static const AssetGenImage markerLocation =
      AssetGenImage('assets/marker_location.png');
  static const AssetGenImage markerStore =
      AssetGenImage('assets/marker_store.png');
  static const AssetGenImage markerStoreNew =
      AssetGenImage('assets/marker_store_new.png');
  static const AssetGenImage noEmail = AssetGenImage('assets/no-email.png');
  static const String notifications24dpE8EAED31 =
      'assets/notifications_24dp_E8EAED (3) 1.svg';
  static const String paymentProcessed = 'assets/payment-processed.svg';
  static const String shopGeneric = 'assets/shop-generic.svg';
  static const AssetGenImage unLock = AssetGenImage('assets/un_lock.png');
  static const String uncheck = 'assets/uncheck.svg';

  /// List of all assets
  static List<dynamic> get values => [
        a3159511881580040079,
        aboutOurTeam,
        appIcon,
        appIcon2,
        avatarDefault,
        buttonSearch,
        checked,
        floatingAdd,
        icHelp,
        iconAdd,
        iconAdd1svg,
        iconBleConnect,
        iconBleConnectedPng,
        iconBleConnectedSvg,
        iconBleConnecting,
        iconBleNotConnect,
        iconDirection,
        iconEditPng,
        iconEditSvg,
        iconEmptyProduct,
        iconNotiPng,
        iconNotiSvg,
        iconRemove,
        image158,
        kitemite1920x10802Png,
        kitemite1920x10802Svg,
        lock24h,
        markerIos,
        markerLocation,
        markerStore,
        markerStoreNew,
        noEmail,
        notifications24dpE8EAED31,
        paymentProcessed,
        shopGeneric,
        unLock,
        uncheck
      ];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}
