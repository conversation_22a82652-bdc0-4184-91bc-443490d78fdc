import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    int? id,
    String? title,
    String? message,
    String? screen,
    String? config,
    String? image,
    String? category,
    @JsonKey(name: 'created_at') DateTime? createdAt,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
    NotificationPivot? pivot,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}

@freezed
class NotificationPivot with _$NotificationPivot {
  const factory NotificationPivot({
    @JsonKey(name: 'user_id') int? userId,
    @Json<PERSON>ey(name: 'notification_id') int? notificationId,
    int? status,
  }) = _NotificationPivot;

  factory NotificationPivot.fromJson(Map<String, dynamic> json) =>
      _$NotificationPivotFromJson(json);
}

extension NotificationModelExtension on NotificationModel {
  bool get isRead => pivot?.status == 1;

  /// Extract product_id from config JSON string
  String? get orderId {
    if (config == null || config!.isEmpty) return null;

    try {
      final configMap = jsonDecode(config!);
      if (configMap is Map<String, dynamic>) {
        return configMap['order_id'].toString();
      }
    } catch (e) {
      // Handle JSON parsing error
      return null;
    }

    return null;
  }

  String? get productId {
    if (config == null || config!.isEmpty) return null;

    try {
      final configMap = jsonDecode(config!);
      if (configMap is Map<String, dynamic>) {
        return configMap['product_id'].toString();
      }
    } catch (e) {
      // Handle JSON parsing error
      return null;
    }

    return null;
  }

  bool get isSoldHistories => screen == 'sold-histories';
}
